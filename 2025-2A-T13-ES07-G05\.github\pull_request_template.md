<!--
INSTRUÇÕES PARA O TÍTULO:
Use um dos prefixos abaixo seguido do título descritivo:

DOCS | (título do PR) - Para documentação, artefatos, relatórios
FEAT | (título do PR) - Para novas funcionalidades, implementações
HOTFIX | (título do PR) - Para correções urgentes
REFACTOR | (título do PR) - Para refatoração de código
TEST | (título do PR) - Para testes e validações
CONFIG | (título do PR) - Para configurações de projeto

Exemplo: "DOCS | Adiciona seção 2.1 - Contexto da Indústria do Parceiro"
-->

# 📝 Changelog
- 
- 
- 

<!--
💡 Dicas para um bom changelog:
- Descreva WHAT foi alterado, não HOW
- Use verbos no passado (ex: "Adicionou", "Corrigiu", "Implementou")
- Seja específico e claro
- Uma linha por mudança principal

Exemplos:
- Adicionou análise de sentimento para detecção de palavrões
- Corrigiu bug na transcrição de áudio em português
- Implementou dashboard básico com métricas de qualidade
-->
