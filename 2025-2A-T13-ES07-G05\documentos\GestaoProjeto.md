<Table>
  <tr>
    <td><a href= "https://www.comgas.com.br/"><img src="img/comgas.png" alt="Comgas" border="0"></td>
    <td>
      <a href= "https://www.inteli.edu.br/"><img src="img/logo-Inteli.png" alt="Inteli - Instituto de Tecnologia e Liderança" border="0"></a>
    </td>
  </tr>
</table>

# Nome do Projeto: <nome do projeto>

## Nome do Grupo: <nome do grupo>

## Integrantes:

- <a href="https://www.linkedin.com/in/anacdejesus//">Ana Carolina de <PERSON> da <PERSON></a>
- <a href="https://www.linkedin.com/in/felipe-zillo-72b367247/"><PERSON></a>
- <a href="https://www.linkedin.com/in/fernandobertholdo/"><PERSON></a>
- <a href="https://www.linkedin.com/in/henrique-botti-6272571a0/"><PERSON><PERSON></a>
- <a href="https://www.linkedin.com/in/karine-victoria//">Karine Victoria Rosa da Paixão</a>
- <a href="https://www.linkedin.com/in/natalycunha /">Nataly de Souza Cunha</a>
- <a href="https://www.linkedin.com/in/tainacortez/">Tainá de Paiva Cortez</a>

# Sumário
- [1. Introdução](#1-introdução)
- [2. Avaliação das Sprints](#2-avaliação-das-sprints)
  - [2.1 Sprint 1](#21-sprint-1)
  - [2.2 Sprint 2](#21-sprint-2)
  - [2.3 Sprint 3](#21-sprint-3)
  - [2.4 Sprint 4](#21-sprint-4)
  - [2.5 Sprint 5](#21-sprint-5)
- [3. Análise de Riscos](#3-análise-de-riscos)
  - [3.1 Riscos identificados](#31-riscos-identificados)
  - [3.2 Mitigação de Riscos](#32-mitigação-de-riscos)
- [4. Análise Post Mortem](#4-análise-post-mortem)
  - [4.1 Sucessos do Projeto](#41-sucessos-do-projeto)
  - [4.2 Oportunidades de Melhoria](#42-oportunidades-de-melhoria)
  - [4.3 Lições Aprendidas](#43-lições-aprendidas)

# 1. Introdução
&emsp; O objetivo deste documento é registrar de maneira estruturada a evolução do projeto desenvolvido pelo grupo ao longo das sprints. Através da análise crítica dos pontos fortes e dos pontos a serem melhorados identificados durante o ciclo de desenvolvimento, este documento visa não apenas acompanhar o desempenho do time, mas também orientar a implementação de ações de melhoria contínua. O registro sistemático dessas avaliações tem como propósito fortalecer a colaboração, a eficiência e a qualidade das entregas do grupo.

&emsp; Além da análise contínua de desempenho nas sprints, este documento também contempla a identificação de riscos que poderiam impactar o projeto, as estratégias de mitigação propostas para cada risco mapeado e, por fim, uma análise *post mortem* que reflete sobre os sucessos alcançados, as oportunidades de melhoria e as lições aprendidas ao longo do processo.

# 2. Avaliação das Sprints
&emsp; Nesta seção, são apresentadas as análises realizadas ao final de cada sprint, baseadas nas *Sprint Reviews* e *Retrospectives*. Para cada sprint, serão mensurados alguns fatores, de acordo com o consenso geral do grupo. Além disso, haverá uma pequena tabela com informações sobre a gestão do projeto, que será preenchida pelo Scrum Master. Essa abordagem permite identificar padrões de comportamento, aprendizados e oportunidades de evolução, proporcionando ao time uma base sólida para aprimorar suas práticas e maximizar a eficácia nas próximas sprints do projeto.

## 2.1 Sprint 1
### Percepção da Equipe *(Notas de 0 a 10)*  

<div align="center">

| Percepções da equipe          | Nota (0–10) |  
|:-----------------------------:|:-----------:|  
| **Comunicação**               |      10     |  
| **Gestão de Cards**           |      7      |  
| **Correção de PR**            |      6      |  
| **Engajamento**               |      9      |  
| **Qualidade das entregas**    |      10     |  
| **Aprendizado**               |      8      |  

</div>

&emsp; De forma geral, a equipe manteve uma comunicação alinhada, o que contribuiu para entregas de qualidade. No entanto, foram identificados desafios na gestão de cards e nas revisões de PRs. Esses pontos impactaram a fluidez do sprint.

### Gestão do Projeto *(Resposta em 1 palavra)*  

<div align="center">

| Gestão do Projeto                    | Resposta |  
|-----------------------------|----------|  
| **Atrasos no projeto?**     |     Não     |  
| **Retrabalhos no projeto?** |     Não     |  
| **Escopo das tasks bem fragmentado?**|     Sim     |  
| **Projeto entregue na data correta?**|     Sim     |  

</div>

## 2.2 Sprint 2
_conteúdo_
 **Nota:** Insira informações sobre mudanças realizadas.

## 2.3 Sprint 3
_conteúdo_
 **Nota:** Insira informações sobre mudanças realizadas.

## 2.4 Sprint 4
_conteúdo_
 **Nota:** Insira informações sobre mudanças realizadas.

## 2.5 Sprint 5
_conteúdo_
 **Nota:** Insira informações sobre mudanças realizadas.

# 3. Análise de Riscos
_conteúdo_

## 3.1 Riscos identificados
_conteúdo_

## 3.2 Mitigação de Riscos
_conteúdo_

# 4. Análise Post Mortem
_conteúdo_

## 4.1 Sucessos do Projeto
_conteúdo_

## 4.2 Oportunidades de Melhoria
_conteúdo_

## 4.3 Lições Aprendidas
_conteúdo_